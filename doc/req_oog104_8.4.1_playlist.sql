-- OOG104-v8.4.1 Web端 Music, Playlist, Music Playlist Collection 建表SQL
-- 创建时间: 2025-09-01
-- 需求: 重做Music和Playlist功能，新增Music Playlist Collection功能 (仅Web端)

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS proj_fitness_playlist_collection_playlist_relation;
DROP TABLE IF EXISTS proj_fitness_playlist_music_relation;
DROP TABLE IF EXISTS proj_fitness_playlist_collection;
DROP TABLE IF EXISTS proj_fitness_playlist;
DROP TABLE IF EXISTS proj_fitness_music;

-- 创建proj_fitness_music表
CREATE TABLE proj_fitness_music (
    id                  INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    music_name          VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '音乐名称',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    type                INT UNSIGNED NOT NULL COMMENT '音乐类型（多选位运算）：1-Soft, 2-Hit, 4-Kpop',
    audio_url           VARCHAR(255) NOT NULL COMMENT '音频文件URL，mp3格式',
    duration            INT          NULL COMMENT '时长(秒)',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NOT NULL COMMENT '创建人',
    create_time         DATETIME     NOT NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_music - 健身音乐表';

-- 为proj_fitness_music表创建索引
CREATE INDEX idx_proj_fitness_music_type ON proj_fitness_music (type);
CREATE INDEX idx_proj_fitness_music_status ON proj_fitness_music (status);
CREATE INDEX idx_proj_fitness_music_proj_id ON proj_fitness_music (proj_id);

-- 创建proj_fitness_playlist表
CREATE TABLE proj_fitness_playlist (
    id                  INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    name                VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '播放列表名称',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    type                INT UNSIGNED NULL COMMENT '播放列表类型（多选位运算，使用FitnessMusicTypeEnums）：1-Soft, 2-Hit, 4-Kpop',
    subscription        TINYINT      DEFAULT 0 NOT NULL COMMENT '是否收费 0不收费 1收费',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NOT NULL COMMENT '创建人',
    create_time         DATETIME     NOT NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_playlist - 健身播放列表表';

-- 为proj_fitness_playlist表创建索引
CREATE INDEX idx_proj_fitness_playlist_type ON proj_fitness_playlist (type);
CREATE INDEX idx_proj_fitness_playlist_status ON proj_fitness_playlist (status);
CREATE INDEX idx_proj_fitness_playlist_proj_id ON proj_fitness_playlist (proj_id);

-- 创建proj_fitness_playlist_collection表
CREATE TABLE proj_fitness_playlist_collection (
    id                  INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    name                VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '合集名称',
    event_name          VARCHAR(127) NULL COMMENT '事件名称(ID+Name)',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    used_for            INT UNSIGNED NULL COMMENT '用途（多选位运算）：1-Regular Fitness, 2-Chair Yoga, 4-Classic Yoga, 8-Wall Pilates, 16-106 Fitness, 32-Pilates',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NOT NULL COMMENT '创建人',
    create_time         DATETIME     NOT NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_playlist_collection - 健身播放列表合集表';

-- 为proj_fitness_playlist_collection表创建索引
CREATE INDEX idx_proj_fitness_collection_used_for ON proj_fitness_playlist_collection (used_for);
CREATE INDEX idx_proj_fitness_collection_status ON proj_fitness_playlist_collection (status);
CREATE INDEX idx_proj_fitness_collection_proj_id ON proj_fitness_playlist_collection (proj_id);

-- 创建播放列表与音乐关联表
CREATE TABLE proj_fitness_playlist_music_relation (
    id                          INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    proj_fitness_playlist_id    INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist id',
    proj_fitness_music_id       INT UNSIGNED NOT NULL COMMENT 'proj_fitness_music id',
    proj_id                     INT UNSIGNED NOT NULL COMMENT '项目id',
    del_flag                    TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user                 VARCHAR(50)  NOT NULL COMMENT '创建人',
    create_time                 DATETIME     NOT NULL COMMENT '创建时间',
    update_user                 VARCHAR(50)  NULL COMMENT '修改人',
    update_time                 DATETIME     NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_playlist_music_relation - 播放列表音乐关联表';

-- 为proj_fitness_playlist_music_relation表创建索引
CREATE INDEX idx_playlist_music_relation_playlist_id ON proj_fitness_playlist_music_relation (proj_fitness_playlist_id);
CREATE INDEX idx_playlist_music_relation_music_id ON proj_fitness_playlist_music_relation (proj_fitness_music_id);
CREATE INDEX idx_playlist_music_relation_proj_id ON proj_fitness_playlist_music_relation (proj_id);

-- 创建播放列表合集与播放列表关联表
CREATE TABLE proj_fitness_playlist_collection_playlist_relation (
    id                                      INT UNSIGNED AUTO_INCREMENT COMMENT 'id' PRIMARY KEY,
    proj_fitness_playlist_collection_id     INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist_collection id',
    proj_fitness_playlist_id                INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist id',
    proj_id                                 INT UNSIGNED NOT NULL COMMENT '项目id',
    del_flag                                TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user                             VARCHAR(50)  NOT NULL COMMENT '创建人',
    create_time                             DATETIME     NOT NULL COMMENT '创建时间',
    update_user                             VARCHAR(50)  NULL COMMENT '修改人',
    update_time                             DATETIME     NULL COMMENT '修改时间'
) COMMENT 'proj_fitness_playlist_collection_playlist_relation - 播放列表合集播放列表关联表';

-- 为proj_fitness_playlist_collection_playlist_relation表创建索引
CREATE INDEX idx_collection_playlist_relation_collection_id ON proj_fitness_playlist_collection_playlist_relation (proj_fitness_playlist_collection_id);
CREATE INDEX idx_collection_playlist_relation_playlist_id ON proj_fitness_playlist_collection_playlist_relation (proj_fitness_playlist_id);
CREATE INDEX idx_collection_playlist_relation_proj_id ON proj_fitness_playlist_collection_playlist_relation (proj_id);

-- 菜单配置 - Fitness Music Manage
BEGIN;
SET @menuName:='Fitness Music Manage';
SET @urlStart:='fitnessMusic';
SET @menuId = 0;
SET @operator = '<EMAIL>';

-- 创建父菜单
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, 0, @menuName, @urlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 21);
SELECT LAST_INSERT_ID() INTO @menuId;

-- Fitness Music 子菜单
SET @musicMenuName:='Fitness Music';
SET @musicUrlStart:='fitnessMusic';
SET @musicMenuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, @musicMenuName, @musicUrlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 1);
SELECT LAST_INSERT_ID() INTO @musicMenuId;

INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'View', CONCAT(@musicUrlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'New', CONCAT(@musicUrlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'Edit', CONCAT(@musicUrlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'Del', CONCAT(@musicUrlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'Export', CONCAT(@musicUrlStart,':export'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @musicMenuId, 'Import', CONCAT(@musicUrlStart,':import'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

-- Fitness Playlist 子菜单
SET @playlistMenuName:='Fitness Playlist';
SET @playlistUrlStart:='fitnessPlaylist';
SET @playlistMenuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, @playlistMenuName, @playlistUrlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 2);
SELECT LAST_INSERT_ID() INTO @playlistMenuId;

INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @playlistMenuId, 'View', CONCAT(@playlistUrlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @playlistMenuId, 'New', CONCAT(@playlistUrlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @playlistMenuId, 'Edit', CONCAT(@playlistUrlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @playlistMenuId, 'Del', CONCAT(@playlistUrlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

-- Fitness Music Playlist Collection 子菜单
SET @collectionMenuName:='Fitness Playlist Collection';
SET @collectionUrlStart:='fitnessPlaylistCollection';
SET @collectionMenuId = 0;
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @menuId, @collectionMenuName, @collectionUrlStart, 1, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 3);
SELECT LAST_INSERT_ID() INTO @collectionMenuId;

INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @collectionMenuId, 'View', CONCAT(@collectionUrlStart,':read'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @collectionMenuId, 'New', CONCAT(@collectionUrlStart,':add'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @collectionMenuId, 'Edit', CONCAT(@collectionUrlStart,':update'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);
INSERT INTO `proj_menu` (`id`, `parent_id`, `menu_name`, `menu_key`, `menu_type`, `path`, `component`, `required`, `visible`, `status`, `icon`, `remark`, `del_flag`, `create_user`, `create_time`, `update_user`, `update_time`, `sort_no`)
VALUES (null, @collectionMenuId, 'Del', CONCAT(@collectionUrlStart,':del'), 2, NULL, NULL, 1, 1, 1, NULL, NULL, 0, @operator, now(), NULL, NULL, 0);

COMMIT;

-- ========================================
-- 创建对应的 _pub 表（用于App端数据源）
-- ========================================

-- 创建proj_fitness_music_pub表
CREATE TABLE proj_fitness_music_pub (
    version             INT          NOT NULL COMMENT '版本',
    id                  INT UNSIGNED NOT NULL COMMENT 'id',
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    music_name          VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '音乐名称',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    type                INT UNSIGNED NOT NULL COMMENT '音乐类型（多选位运算）：1-Soft, 2-Hit, 4-Kpop',
    audio_url           VARCHAR(255) NOT NULL COMMENT '音频文件URL，mp3格式',
    duration            INT          NULL COMMENT '时长(秒)',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NULL COMMENT '创建人',
    create_time         DATETIME     NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间',
    PRIMARY KEY (version, id)
) COMMENT 'proj_fitness_music_pub - 健身音乐发布表';

-- 创建proj_fitness_playlist_pub表
CREATE TABLE proj_fitness_playlist_pub (
    version             INT          NOT NULL COMMENT '版本',
    id                  INT UNSIGNED NOT NULL COMMENT 'id',
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    name                VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '播放列表名称',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    type                INT UNSIGNED NULL COMMENT '播放列表类型（多选位运算，使用FitnessMusicTypeEnums）：1-Soft, 2-Hit, 4-Kpop',
    subscription        TINYINT      DEFAULT 0 NOT NULL COMMENT '是否收费 0不收费 1收费',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NULL COMMENT '创建人',
    create_time         DATETIME     NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间',
    PRIMARY KEY (version, id)
) COMMENT 'proj_fitness_playlist_pub - 健身播放列表发布表';

-- 创建proj_fitness_playlist_collection_pub表
CREATE TABLE proj_fitness_playlist_collection_pub (
    version             INT          NOT NULL COMMENT '版本',
    id                  INT UNSIGNED NOT NULL COMMENT 'id',
    table_code          TINYINT      NULL COMMENT '表标识',
    proj_id             INT UNSIGNED NOT NULL COMMENT '项目id',
    name                VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '合集名称',
    event_name          VARCHAR(127) NULL COMMENT '事件名称(ID+Name)',
    cover_img_url       VARCHAR(255) NULL COMMENT '封面图片URL，支持png/webp格式',
    detail_img_url      VARCHAR(255) NULL COMMENT '详情图片URL，支持png/webp格式，隐藏字段',
    used_for            INT UNSIGNED NULL COMMENT '用途（多选位运算）：1-Regular Fitness, 2-Chair Yoga, 4-Classic Yoga, 8-Wall Pilates, 16-106 Fitness, 32-Pilates',
    status              TINYINT      DEFAULT 0 NOT NULL COMMENT '状态',
    del_flag            TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user         VARCHAR(50)  NULL COMMENT '创建人',
    create_time         DATETIME     NULL COMMENT '创建时间',
    update_user         VARCHAR(50)  NULL COMMENT '修改人',
    update_time         DATETIME     NULL COMMENT '修改时间',
    PRIMARY KEY (version, id),
    KEY idx_fitness_playlist_collection_used_for (used_for)
) COMMENT 'proj_fitness_playlist_collection_pub - 健身播放列表合集发布表';

-- 创建播放列表与音乐关联发布表
CREATE TABLE proj_fitness_playlist_music_relation_pub (
    version                     INT          NOT NULL COMMENT '版本',
    id                          INT UNSIGNED NOT NULL COMMENT 'id',
    proj_fitness_playlist_id    INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist id',
    proj_fitness_music_id       INT UNSIGNED NOT NULL COMMENT 'proj_fitness_music id',
    proj_id                     INT UNSIGNED NOT NULL COMMENT '项目id',
    del_flag                    TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user                 VARCHAR(50)  NULL COMMENT '创建人',
    create_time                 DATETIME     NULL COMMENT '创建时间',
    update_user                 VARCHAR(50)  NULL COMMENT '修改人',
    update_time                 DATETIME     NULL COMMENT '修改时间',
    PRIMARY KEY (version, id),
    KEY idx_playlist_music_relation_playlist_id (proj_fitness_playlist_id)
) COMMENT 'proj_fitness_playlist_music_relation_pub - 播放列表音乐关联发布表';

-- 创建播放列表合集与播放列表关联发布表
CREATE TABLE proj_fitness_playlist_collection_playlist_relation_pub (
    version                                 INT          NOT NULL COMMENT '版本',
    id                                      INT UNSIGNED NOT NULL COMMENT 'id',
    proj_fitness_playlist_collection_id     INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist_collection id',
    proj_fitness_playlist_id                INT UNSIGNED NOT NULL COMMENT 'proj_fitness_playlist id',
    proj_id                                 INT UNSIGNED NOT NULL COMMENT '项目id',
    del_flag                                TINYINT      DEFAULT 0 NOT NULL COMMENT '删除标识 0 未删除，1已删除',
    create_user                             VARCHAR(50)  NULL COMMENT '创建人',
    create_time                             DATETIME     NULL COMMENT '创建时间',
    update_user                             VARCHAR(50)  NULL COMMENT '修改人',
    update_time                             DATETIME     NULL COMMENT '修改时间',
    PRIMARY KEY (version, id),
    KEY idx_collection_playlist_relation_collection_id (proj_fitness_playlist_collection_id)
) COMMENT 'proj_fitness_playlist_collection_playlist_relation_pub - 播放列表合集播放列表关联发布表';
