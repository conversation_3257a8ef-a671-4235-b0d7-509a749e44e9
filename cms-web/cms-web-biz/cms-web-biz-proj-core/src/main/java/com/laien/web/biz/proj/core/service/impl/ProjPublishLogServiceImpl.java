package com.laien.web.biz.proj.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.laien.web.biz.core.constant.RedisKeyConstant;
import com.laien.web.biz.proj.core.bo.CloudflarePurgeCacheResult;
import com.laien.web.biz.proj.core.bo.ProjPublishProjParamBO;
import com.laien.web.biz.proj.core.bo.ProjPublishProjRelationParamBO;
import com.laien.web.biz.proj.core.config.CloudflareApiConfig;
import com.laien.web.biz.proj.core.entity.ProjInfo;
import com.laien.web.biz.proj.core.entity.ProjPublishCurrentVersion;
import com.laien.web.biz.proj.core.entity.ProjPublishLog;
import com.laien.web.biz.proj.core.mapper.ProjPublishLogMapper;
import com.laien.web.biz.proj.core.request.ProjPublishPubOnlineReq;
import com.laien.web.biz.proj.core.service.IProjAppRequestUriService;
import com.laien.web.biz.proj.core.service.IProjInfoService;
import com.laien.web.biz.proj.core.service.IProjPublishCurrentVersionService;
import com.laien.web.biz.proj.core.service.IProjPublishLogService;
import com.laien.web.frame.constant.GlobalConstant;
import com.laien.web.frame.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 发布日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-24
 */
@Slf4j
@Service
@RefreshScope
@EnableAsync
public class ProjPublishLogServiceImpl extends ServiceImpl<ProjPublishLogMapper, ProjPublishLog> implements IProjPublishLogService {

    @Resource
    private IProjPublishCurrentVersionService projPublishCurrentVersionService;
    @Resource
    private IProjAppRequestUriService projAppRequestUriService;
    @Resource
    private IProjInfoService projInfoService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private ProjPublishLogMapper projPublishLogMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CloudflareApiConfig cloudflareApiConfig;

    @Lazy
    @Resource
    private IProjPublishLogService projPublishLogService;

    private String success = "Success";
    private String fail = "Fail";

    private static final String YOGA_REGULAR_WORKOUT_I18N_TABLE_NAME = "proj_yoga_regular_workout_i18n";

    private static final String COLLECTION_CLASS_I18N_TABLE_NAME = "proj_collection_class_i18n";

    private static final String COLLECTION_TEACHER_I18N_TABLE_NAME = "proj_collection_teacher_i18n";

    private static final String YOGA_POSE_WORKOUT = "proj_yoga_pose_workout";
    private static final String YOGA_POSE_GROUP = "proj_yoga_pose_group";
    private static final String YOGA_POSE_GROUP_WORKOUT_RELATION = "proj_yoga_pose_group_workout_relation";
    private static final String YOGA_POSE_LEVEL = "proj_yoga_pose_level";
    private static final String YOGA_POSE_LEVEL_GROUP_RELATION = "proj_yoga_pose_level_group_relation";
    private static final String YOGA_POSE_GROUP_ID = "proj_yoga_pose_group_id";
    private static final String YOGA_POSE_LEVEL_ID = "proj_yoga_pose_level_id";
    private static final String PROJ_AUTO_WORKOUT_BASIC_INFO = "proj_auto_workout_basic_info";
    private static final String PROJ_AUTO_WORKOUT_BASIC_INFO_WORKOUT_RELATION = "proj_auto_workout_basic_info_workout_relation";
    private static final String PROJ_AUTO_WORKOUT_BASIC_INFO_ID = "proj_auto_workout_basic_info_id";
    private static final String PROJ_YOGA_QUOTE = "proj_yoga_quote";
    private static final String PROJ_WALL_PILATES_REGULAR_WORKOUT = "proj_wall_pilates_regular_workout";
    private static final String PROJ_WALL_PILATES_REGULAR_WORKOUT_VIDEO_RELATION = "proj_wall_pilates_regular_workout_video_relation";
    private static final String PROJ_WALL_PILATES_REGULAR_WORKOUT_ID = "proj_wall_pilates_regular_workout_id";
    private static final String PROJ_CHAIR_YOGA_REGULAR_WORKOUT = "proj_chair_yoga_regular_workout";
    private static final String PROJ_CHAIR_YOGA_REGULAR_WORKOUT_VIDEO_RELATION = "proj_chair_yoga_regular_workout_video_relation";
    private static final String PROJ_CHAIR_YOGA_REGULAR_WORKOUT_ID = "proj_chair_yoga_regular_workout_id";
    private static final String PROJ_YOGA_REGULAR_WORKOUT_AUDIO_I18N = "proj_yoga_regular_workout_audio_i18n";
    private static final String PROJ_YOGA_REGULAR_CATEGORY = "proj_yoga_regular_category";
    private static final String PROJ_YOGA_REGULAR_WORKOUT_CATEGORY_RELATION = "proj_yoga_regular_workout_category_relation";
    private static final String PROJ_FITNESS_WORKOUT_IMAGE = "proj_fitness_workout_image";
    private static final String PROJ_FITNESS_TEMPLATE = "proj_fitness_template";
    private static final String PROJ_FITNESS_MANUAL_WORKOUT = "proj_fitness_manual_workout";
    private static final String PROJ_FITNESS_MANUAL_WORKOUT_I18N = "proj_fitness_manual_workout_i18n";
    private static final String PROJ_FITNESS_MANUAL_WORKOUT_EXERCISE_VIDEO = "proj_fitness_manual_workout_exercise_video";

    private static final String PROJ_YOGA_PROGRAM = "proj_yoga_program";
    private static final String PROJ_YOGA_PROGRAM_RELATION = "proj_yoga_program_relation";
    private static final String PROJ_YOGA_PROGRAM_ID = "proj_yoga_program_id";

    private static final String PROJ_YOGA_PROGRAM_TYPE = "proj_yoga_program_type";
    private static final String PROJ_YOGA_PROGRAM_TYPE_RELATION = "proj_yoga_program_type_relation";
    private static final String PROJ_YOGA_PROGRAM_TYPE_ID = "proj_yoga_program_type_id";

    private static final String PROJ_YOGA_PROGRAM_LEVEL = "proj_yoga_program_level";
    private static final String PROJ_YOGA_PROGRAM_LEVEL_RELATION = "proj_yoga_program_level_relation";
    private static final String PROJ_YOGA_PROGRAM_LEVEL_ID = "proj_yoga_program_level_id";

    private static final String PROJ_YOGA_PROGRAM_CATEGORY = "proj_yoga_program_category";
    private static final String PROJ_YOGA_PROGRAM_CATEGORY_RELATION = "proj_yoga_program_category_relation";
    private static final String PROJ_YOGA_PROGRAM_CATEGORY_ID = "proj_yoga_program_category_id";
    private static final String PROJ_FASTING_ARTICLE = "proj_fasting_article";

    private static final String PROJ_UNIT = "proj_unit";
    private static final String PROJ_ALLERGEN = "proj_allergen";
    private static final String PROJ_ALLERGEN_RELATION = "proj_allergen_relation";
    private static final String PROJ_DISH = "proj_dish";
    private static final String PROJ_INGREDIENT = "proj_ingredient";
    private static final String PROJ_DISH_STEP = "proj_dish_step";
    private static final String PROJ_DISH_STEP_TIP = "proj_dish_step_tip";

    private static final String PROJ_MEAL_PLAN = "proj_meal_plan";
    private static final String PROJ_MEAL_PLAN_RELATION = "proj_meal_plan_relation";
    private static final String PROJ_MEAL_PLAN_ID = "proj_meal_plan_id";

    private static final String PROJ_DISH_COLLECTION = "proj_dish_collection";
    private static final String PROJ_DISH_COLLECTION_RELATION = "proj_dish_collection_relation";
    private static final String PROJ_DISH_COLLECTION_ID = "proj_dish_collection_id";

    private static final String PROJ_FITNESS_UNIT = "proj_fitness_unit";
    private static final String PROJ_FITNESS_ALLERGEN = "proj_fitness_allergen";
    private static final String PROJ_FITNESS_ALLERGEN_RELATION = "proj_fitness_allergen_relation";
    private static final String PROJ_FITNESS_DISH = "proj_fitness_dish";
    private static final String PROJ_FITNESS_INGREDIENT = "proj_fitness_ingredient";
    private static final String PROJ_FITNESS_DISH_STEP = "proj_fitness_dish_step";
    private static final String PROJ_FITNESS_DISH_STEP_TIP = "proj_fitness_dish_step_tip";

    private static final String PROJ_FITNESS_COACHING_COURSES = "proj_fitness_coaching_courses";
    private static final String PROJ_FITNESS_COACH = "proj_fitness_coach";
    private static final String PROJ_FITNESS_COACHING_COURSES_RELATION = "proj_fitness_coaching_courses_relation";
    private static final String PROJ_FITNESS_VIDEO_COURSE = "proj_fitness_video_course";
    private static final String PROJ_FITNESS_FASTING_ARTICLE = "proj_fitness_fasting_article";


    private static final String PROJ_FITNESS_MEAL_PLAN = "proj_fitness_meal_plan";
    private static final String PROJ_FITNESS_MEAL_PLAN_RELATION = "proj_fitness_meal_plan_relation";

    private static final String PROJ_FITNESS_DISH_COLLECTION = "proj_fitness_dish_collection";
    private static final String PROJ_FITNESS_DISH_COLLECTION_RELATION = "proj_fitness_dish_collection_relation";

    private static final String PROJ_FITNESS_CHALLENGE = "proj_fitness_challenge";
    private static final String PROJ_FITNESS_CHALLENGE_MANUAL_WORKOUT_RELATION = "proj_fitness_challenge_manual_workout_relation";

    private static final String PROJ_YOGA_MUSIC = "proj_yoga_music";

    private static final String PROJ_YOGA_PLAYLIST = "proj_yoga_playlist";
    private static final String PROJ_YOGA_PLAYLIST_RELATION = "proj_yoga_playlist_relation";
    private static final String PROJ_YOGA_PLAYLIST_ID = "proj_yoga_playlist_id";

    private static final String PROJ_PROGRAM116 = "proj_program116";
    private static final String PROJ_COACH116 = "proj_coach116";
    private static final String PROJ_PROGRAM116_RELATION = "proj_program116_relation";

    private static final String PROJ_RECOVERY_CATEGORY116 = "proj_recovery_category116";
    private static final String PROJ_RECOVERY_CATEGORY116_ID = "proj_recovery_category116_id";
    private static final String PROJ_RECOVERY_CATEGORY116_RELATION = "proj_recovery_category116_proj_workout116";

    private static final String PROJ_FITNESS_MUSIC = "proj_fitness_music";
    private static final String PROJ_FITNESS_PLAYLIST = "proj_fitness_playlist";
    private static final String PROJ_FITNESS_PLAYLIST_MUSIC_RELATION = "proj_fitness_playlist_music_relation";
    private static final String PROJ_FITNESS_PLAYLIST_COLLECTION = "proj_fitness_playlist_collection";
    private static final String PROJ_FITNESS_PLAYLIST_COLLECTION_PLAYLIST_RELATION = "proj_fitness_playlist_collection_playlist_relation";

    @Override
    public ProjPublishCurrentVersion getVersionByProjId(Integer projId) {
        LambdaQueryWrapper<ProjPublishCurrentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProjPublishCurrentVersion::getProjId, projId);
        ProjPublishCurrentVersion publishCurrentVersion = projPublishCurrentVersionService.getOne(wrapper);
        if (Objects.isNull(publishCurrentVersion)) {
            publishCurrentVersion = new ProjPublishCurrentVersion();
            publishCurrentVersion.setCurrentVersion(0);
            publishCurrentVersion.setProjId(-1);
            publishCurrentVersion.setAppCode("not set");
        }
        return publishCurrentVersion;
    }

    @Override
    public boolean getLastPubIsPre(Integer projId) {
        LambdaQueryWrapper<ProjPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPublishLog::getProjId, projId)
                //增加过滤单独清理缓存的日志的条件
                .ne(ProjPublishLog::getPrePublish, GlobalConstant.TWO)
                .orderByDesc(ProjPublishLog::getId)
                .last("LIMIT 5");
        List<ProjPublishLog> list = this.list(queryWrapper);
        if (!list.isEmpty()) {
            // 目前只关注最后一次发布是否是预发布
            ProjPublishLog publishLog = list.get(GlobalConstant.ZERO);
            return Objects.equals(publishLog.getPrePublish(), GlobalConstant.ONE);
        }
        return false;
    }

    /**
     * 修改版本号
     *
     * @param projId         projId
     * @param publishVersion publishVersion
     */
    @Override
    public void updateVersionByProjId (Integer projId, Integer publishVersion) {
        LambdaUpdateWrapper<ProjPublishCurrentVersion> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProjPublishCurrentVersion::getCurrentVersion, publishVersion);
        wrapper.eq(ProjPublishCurrentVersion::getProjId, projId);
        boolean flag = projPublishCurrentVersionService.update(new ProjPublishCurrentVersion(), wrapper);
        if (!flag) {
            ProjInfo projInfo = projInfoService.getById(projId);
            ProjPublishCurrentVersion currentVersion = new ProjPublishCurrentVersion();
            currentVersion.setCurrentVersion(publishVersion);
            currentVersion.setAppCode(projInfo.getAppCode());
            currentVersion.setProjId(projId);
            projPublishCurrentVersionService.save(currentVersion);
        }

    }

    @Override
    public Integer savePublish(Integer projId, ProjPublishPubOnlineReq projPublishPubOnlineReq) {
        String lockKey = "LOCK:CMS:PUBLISH:" + projId;
        RLock lock = redissonClient.getLock(lockKey);
        Integer publishVersion = null;
        List<ProjPublishProjParamBO> projParamBOList = new ArrayList<>();
        List<ProjPublishProjRelationParamBO> projRelationParamBOList = new ArrayList<>();
        boolean prePublish = projPublishPubOnlineReq.getPrePublish() != null && projPublishPubOnlineReq.getPrePublish();
        int prePublishCode = prePublish ? GlobalConstant.ONE : GlobalConstant.ZERO;
        ProjInfo projInfo = projInfoService.getById(projId);
        if (projInfo == null) {
            throw new BizException("project not found");
        }
        // 加锁
        if (lock.tryLock()) {
            try {
                // 查询旧的版本号
                ProjPublishCurrentVersion currentVersion = this.getVersionByProjId(projId);
                HttpHeaders headers = new HttpHeaders();
                if (prePublish) {
                    publishVersion = GlobalConstant.VERSION_PRE_RELEASE;
                    // 告诉python 执行预发布
                    headers.set(GlobalConstant.HEADER_APPVERSION, publishVersion.toString());
                } else {
                    Integer oldVersion = currentVersion.getCurrentVersion();
                    // 加1 作为本次发布的版本
                    publishVersion = oldVersion + 1;
                }

                // 发布项目表
                projParamBOList.add(new ProjPublishProjParamBO("proj_playlist", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_template", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_pose_library", projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO("proj_collection_class", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_workout116", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_category116", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_template116", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_yoga_regular_workout", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_yoga_auto_workout_template", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_workout", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_workout_i18n", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_workout_proj_fitness_video", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_plan", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_plan_proj_fitness_workout", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_collection", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_fitness_collection_proj_fitness_workout", projId, publishVersion));
                //2025/03/21 增加oog104相关表发布
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_MANUAL_WORKOUT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_MANUAL_WORKOUT_I18N, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_TEMPLATE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_WORKOUT_IMAGE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(YOGA_POSE_WORKOUT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(YOGA_POSE_GROUP, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(YOGA_POSE_LEVEL, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_AUTO_WORKOUT_BASIC_INFO, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_QUOTE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_WALL_PILATES_REGULAR_WORKOUT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_CHAIR_YOGA_REGULAR_WORKOUT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_REGULAR_WORKOUT_AUDIO_I18N, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_REGULAR_CATEGORY, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_REGULAR_WORKOUT_CATEGORY_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_PROGRAM, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_PROGRAM_TYPE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_PROGRAM_CATEGORY, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_PROGRAM_LEVEL, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FASTING_ARTICLE, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_UNIT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_ALLERGEN, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_ALLERGEN_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_DISH, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_INGREDIENT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_DISH_STEP, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_DISH_STEP_TIP, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_MEAL_PLAN, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_DISH_COLLECTION, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_UNIT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_ALLERGEN, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_ALLERGEN_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_DISH, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_INGREDIENT, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_DISH_STEP, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_DISH_STEP_TIP, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_MEAL_PLAN, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_MEAL_PLAN_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_DISH_COLLECTION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_DISH_COLLECTION_RELATION, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_CHALLENGE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_CHALLENGE_MANUAL_WORKOUT_RELATION, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_COACHING_COURSES, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_COACH, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_VIDEO_COURSE, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_FASTING_ARTICLE, projId, publishVersion));

                // 2025-09-01 新增fitness music playlist相关表发布
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_MUSIC, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_PLAYLIST, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_PLAYLIST_COLLECTION, projId, publishVersion));
                // 关联表也当作普通表发布，因为它们有projId字段
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_PLAYLIST_MUSIC_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_FITNESS_PLAYLIST_COLLECTION_PLAYLIST_RELATION, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_MUSIC, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_YOGA_PLAYLIST, projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(PROJ_PROGRAM116, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_PROGRAM116_RELATION, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_COACH116, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(PROJ_RECOVERY_CATEGORY116, projId, publishVersion));

                //2024-03-26 新增教练表
                projParamBOList.add(new ProjPublishProjParamBO("proj_collection_teacher", projId, publishVersion));



                // 翻译相关
                projParamBOList.add(new ProjPublishProjParamBO("proj_playlist_i18n", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_playlist_music_i18n", projId, publishVersion));

                projParamBOList.add(new ProjPublishProjParamBO(YOGA_REGULAR_WORKOUT_I18N_TABLE_NAME, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(COLLECTION_CLASS_I18N_TABLE_NAME, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO(COLLECTION_TEACHER_I18N_TABLE_NAME, projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_category116_i18n", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_workout116_i18n", projId, publishVersion));
                projParamBOList.add(new ProjPublishProjParamBO("proj_workout116_res_video116_i18n", projId, publishVersion));


                // 发布项目关联表
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO("proj_playlist_music", projId, publishVersion, "proj_playlist_id", "proj_playlist"));

                projRelationParamBOList.add(new ProjPublishProjRelationParamBO("proj_collection_class_relation", projId, publishVersion, "collection_class_id", "proj_collection_class"));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO("proj_workout116_res_video116", projId, publishVersion, "proj_workout116_id", "proj_workout116"));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO("proj_category116_proj_workout116", projId, publishVersion, "proj_category116_id", "proj_category116"));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO("proj_yoga_regular_workout_video_relation", projId, publishVersion, "proj_yoga_regular_workout_id", "proj_yoga_regular_workout"));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(YOGA_POSE_GROUP_WORKOUT_RELATION, projId, publishVersion, YOGA_POSE_GROUP_ID, YOGA_POSE_GROUP));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(YOGA_POSE_LEVEL_GROUP_RELATION, projId, publishVersion, YOGA_POSE_LEVEL_ID, YOGA_POSE_LEVEL));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_AUTO_WORKOUT_BASIC_INFO_WORKOUT_RELATION, projId, publishVersion, PROJ_AUTO_WORKOUT_BASIC_INFO_ID, PROJ_AUTO_WORKOUT_BASIC_INFO));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_WALL_PILATES_REGULAR_WORKOUT_VIDEO_RELATION, projId, publishVersion, PROJ_WALL_PILATES_REGULAR_WORKOUT_ID, PROJ_WALL_PILATES_REGULAR_WORKOUT));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_CHAIR_YOGA_REGULAR_WORKOUT_VIDEO_RELATION, projId, publishVersion, PROJ_CHAIR_YOGA_REGULAR_WORKOUT_ID, PROJ_CHAIR_YOGA_REGULAR_WORKOUT));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_YOGA_PROGRAM_RELATION, projId, publishVersion, PROJ_YOGA_PROGRAM_ID, PROJ_YOGA_PROGRAM));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_YOGA_PROGRAM_TYPE_RELATION, projId, publishVersion, PROJ_YOGA_PROGRAM_TYPE_ID, PROJ_YOGA_PROGRAM_TYPE));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_YOGA_PROGRAM_LEVEL_RELATION, projId, publishVersion, PROJ_YOGA_PROGRAM_LEVEL_ID, PROJ_YOGA_PROGRAM_LEVEL));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_YOGA_PROGRAM_CATEGORY_RELATION, projId, publishVersion, PROJ_YOGA_PROGRAM_CATEGORY_ID, PROJ_YOGA_PROGRAM_CATEGORY));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_MEAL_PLAN_RELATION, projId, publishVersion, PROJ_MEAL_PLAN_ID, PROJ_MEAL_PLAN));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_DISH_COLLECTION_RELATION, projId, publishVersion, PROJ_DISH_COLLECTION_ID, PROJ_DISH_COLLECTION));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_YOGA_PLAYLIST_RELATION, projId, publishVersion, PROJ_YOGA_PLAYLIST_ID, PROJ_YOGA_PLAYLIST));

                // 2025-03-21 新增oog104相关表发布
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_FITNESS_MANUAL_WORKOUT_EXERCISE_VIDEO, projId, publishVersion, "proj_fitness_manual_workout_id", PROJ_FITNESS_MANUAL_WORKOUT));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_FITNESS_COACHING_COURSES_RELATION, projId, publishVersion, "proj_fitness_coaching_courses_id", PROJ_FITNESS_COACHING_COURSES));
                projRelationParamBOList.add(new ProjPublishProjRelationParamBO(PROJ_RECOVERY_CATEGORY116_RELATION, projId, publishVersion, PROJ_RECOVERY_CATEGORY116_ID, PROJ_RECOVERY_CATEGORY116));

                // 2025-09-01 关联表已改为普通表发布方式，因为它们有projId字段


                // 如果是预发布，需要先删除历史数据
                if (prePublish) {
                    this.deletePrePublishHistory(projId, projParamBOList, projRelationParamBOList);
                }
                for (ProjPublishProjParamBO projParamBO : projParamBOList) {
                    projPublishLogMapper.insertProjToCmsApp(projParamBO);
                }
                for (ProjPublishProjRelationParamBO projRelationParamBO : projRelationParamBOList) {
                    projPublishLogMapper.insertProjRelationToCmsApp(projRelationParamBO);
                }

                // 预发布不需要的操作
                if (!prePublish) {
                    // 修改项目版本为最新的版本号
                    this.updateVersionByProjId(projId, publishVersion);
                }

                // 保存当期发布记录
                ProjPublishLog publishLog = new ProjPublishLog();
                publishLog.setProjId(projId);
                publishLog.setRemark(projPublishPubOnlineReq.getRemark());
                publishLog.setVersion(publishVersion);
                publishLog.setPrePublish(prePublishCode);
                publishLog.setResult(success);
                this.save(publishLog);

                // 预发布不需要的操作
                if (!prePublish) {
                    // 清除redis 移动端项目版本信息
                    String redisKey = RedisKeyConstant.APP_PROJECT_PUBLISH + currentVersion.getAppCode().toLowerCase();
                    redissonClient.getBucket(redisKey).delete();
                }
                // 删除移动端接口缓存，添加version 移动端不需要清缓存
//                redissonClient.getKeys().deleteByPattern(RedisKeyConstant.APP_PROJECT_KEY_PATTERN);
                // 预发布不需要的操作，放到最后，避免Cloudflare 清除，redis没清除
                if (!prePublish) {
                    projPublishLogService.purgeCloudflareCache(projId);
                }
                return publishVersion;
            } catch (Exception e) {
                e.printStackTrace();
                if (publishVersion != null) {
                    // 保存当期发布记录
                    ProjPublishLog publishLog = new ProjPublishLog();
                    publishLog.setProjId(projId);
                    publishLog.setRemark(projPublishPubOnlineReq.getRemark());
                    publishLog.setVersion(publishVersion);
                    publishLog.setPrePublish(prePublishCode);
                    publishLog.setResult(fail);
                    String msg = e.getMessage();
                    String saveMsg = msg;
                    int dbMaxLen = 3000;
                    if (msg != null && msg.length() > dbMaxLen) {
                        saveMsg = msg.substring(0, dbMaxLen);
                    }
                    publishLog.setFailReason(saveMsg);
                    this.save(publishLog);
                }
                if (e instanceof BizException) {
                    throw e;
                } else {
                    throw new BizException("An error occurred in publishing");
                }
            } finally {
                lock.unlock();
                // 预发布不删除发布产生的历史数据
                if (!prePublish) {
                    // 删除历史记录
                    projPublishLogService.deletePubHistory(projId, projParamBOList, projRelationParamBOList);
                }
            }
        } else {
            throw new BizException("Someone is publishing in the current period, Please operate later");
        }

    }


    @Async
    @Override
    public void deletePubHistory(Integer projId, List<ProjPublishProjParamBO> projParamBOList, List<ProjPublishProjRelationParamBO> projRelationParamBOList) {
        if (projId == null) {
            return;
        }
        if (projParamBOList == null) {
            projParamBOList = new ArrayList<>();
        }
        if (projRelationParamBOList == null) {
            projRelationParamBOList = new ArrayList<>();
        }
        if (projParamBOList.isEmpty() && projRelationParamBOList.isEmpty()) {
            return;
        }

        // 保留最近5次发布成功的数据，发布失败不保留
        LambdaQueryWrapper<ProjPublishLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjPublishLog::getProjId, projId)
                .eq(ProjPublishLog::getResult, success)
                .eq(ProjPublishLog::getPrePublish, GlobalConstant.ZERO)
                .orderByDesc(ProjPublishLog::getId)
                .last("LIMIT 5");
        List<ProjPublishLog> list = this.list(queryWrapper);
        if (list.isEmpty()) {
            return;
        }

        // delete not in excludeVersionList
        List<Integer> excludeVersionList = new ArrayList<>();
        for (ProjPublishLog publishLog : list) {
            excludeVersionList.add(publishLog.getVersion());
        }
        excludeVersionList.add(GlobalConstant.VERSION_PRE_RELEASE);

        // 注意： 先删除关系表在删除主表 关联表的删除需要关联主表， 注意inOrNot参数值属于高危操作
        for (ProjPublishProjRelationParamBO projRelationParamBO : projRelationParamBOList) {
            projPublishLogMapper.deleteProjRelationToCmsApp(projRelationParamBO, excludeVersionList, false);
        }
        for (ProjPublishProjParamBO projParamBO : projParamBOList) {
            projPublishLogMapper.deleteProjToCmsApp(projParamBO, excludeVersionList, false);
        }

    }

    @Override
    public void deletePrePublishHistory(Integer projId, List<ProjPublishProjParamBO> projParamBOList, List<ProjPublishProjRelationParamBO> projRelationParamBOList) {
        if (projId == null) {
            return;
        }
        if (projParamBOList == null) {
            projParamBOList = new ArrayList<>();
        }
        if (projRelationParamBOList == null) {
            projRelationParamBOList = new ArrayList<>();
        }
        if (projParamBOList.isEmpty() && projRelationParamBOList.isEmpty()) {
            return;
        }

        // delete in versionList
        List<Integer> versionList = new ArrayList<>();
        versionList.add(GlobalConstant.VERSION_PRE_RELEASE);

        // 注意： 先删除关系表在删除主表 关联表的删除需要关联主表， 注意inOrNot参数值属于高危操作
        for (ProjPublishProjRelationParamBO projRelationParamBO : projRelationParamBOList) {
            projPublishLogMapper.deleteProjRelationToCmsApp(projRelationParamBO, versionList, true);
        }
        for (ProjPublishProjParamBO projParamBO : projParamBOList) {
            projPublishLogMapper.deleteProjToCmsApp(projParamBO, versionList, true);
        }

    }

    @Override
    public void purgeCloudflareCache(Integer projId) {
        ProjInfo projInfo = projInfoService.getById(projId);
        String prefixUrl = projInfo.getPrefixUrl();
        if(StrUtil.isBlank(prefixUrl)){
            log.error("prefixUrl is blank,projId:{}",projId);
            return;
        }
        Set<String> prefixUrlSet = new HashSet<>(32);
        for (String url : StrUtil.split(prefixUrl, ",")) {
            prefixUrlSet.add(StrUtil.trim(url));
        }
        if (CollUtil.isEmpty(prefixUrlSet)) {
            log.error("prefix url set is empty,projId:{}", projId);
            return;
        }
        purgeCache(prefixUrlSet);
    }

    /**
     * cloudflare清除缓存
     *
     * @param prefixUrlSet urlList
     */
    private void purgeCache(Set<String> prefixUrlSet) {

        // 线上环境cloudflare需要清缓存
        // url 认证邮箱，认证key
        String url = cloudflareApiConfig.getPurgeCacheUrl();
        String authEmail = cloudflareApiConfig.getXAuthEmail();
        String authKey = cloudflareApiConfig.getXAuthKey();

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("X-Auth-Email", authEmail);
        httpHeaders.set("X-Auth-key", authKey);

        // 每次清缓存，最多30个
        Map<String, Set<String>> requestBody = new HashMap<>();

        requestBody.put("prefixes", prefixUrlSet);
        HttpEntity<Map<String, Set<String>>> httpEntity = new HttpEntity<>(requestBody, httpHeaders);
        ResponseEntity<CloudflarePurgeCacheResult> responseEntity = restTemplate.postForEntity(url, httpEntity, CloudflarePurgeCacheResult.class);
        CloudflarePurgeCacheResult purgeCacheResult = responseEntity.getBody();
        if (purgeCacheResult == null || purgeCacheResult.getSuccess() == null || !purgeCacheResult.getSuccess()) {
            log.error("Cloudflare清除缓存失败,result:{}",purgeCacheResult);
            throw new BizException("Cloudflare清除缓存失败");
        }
    }


    @Override
    public void savePurgeCacheLog(Integer projId) {
        // 保存清理cf缓存的log
        ProjPublishLog publishLog = new ProjPublishLog();
        publishLog.setProjId(projId);
        publishLog.setRemark("单独清理APP请求CDN缓存");
        publishLog.setVersion(GlobalConstant.ZERO);
        //新类型，与予发布和发布隔离
        publishLog.setPrePublish(GlobalConstant.TWO);
        publishLog.setResult(success);
        this.save(publishLog);
    }

}
